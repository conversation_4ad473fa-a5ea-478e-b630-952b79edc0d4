{"name": "crypto-indicator-web", "version": "0.1.0", "private": true, "proxy": "http://*************:6701", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "darkreader": "^4.9.105", "dotenv": "^16.5.0", "lightweight-charts": "^5.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.1.0", "@types/node": "^18.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "dependency-cruiser": "^16.10.3", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.3", "eslint-plugin-unicorn": "^55.0.0", "knip": "^5.61.2", "madge": "^8.0.0", "prettier": "^3.1.1", "stylelint": "^16.21.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard": "^38.0.0", "stylelint-declaration-strict-value": "^1.10.11", "stylelint-order": "^7.0.0", "stylelint-use-logical": "^2.1.2", "typescript": "^4.9.5"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "generate-client": "node scripts/generate-client.js", "generate-client:dev": "npm run generate-client && echo 'Client generated successfully!'", "generate-client:build": "npm run generate-client", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "lint:css": "stylelint 'src/**/*.css'", "lint:css:check": "stylelint 'src/**/*.css'", "lint:css:fix": "stylelint 'src/**/*.css' --fix", "lint:all": "npm run lint:check && npm run lint:css:check", "lint:all:fix": "npm run lint:fix && npm run lint:css:fix", "type-check": "tsc --noEmit", "analyze:unused": "knip", "analyze:unused:fix": "knip --fix", "analyze:deps": "dependency-cruiser src --validate .dependency-cruiser.js", "analyze:deps:graph": "dependency-cruiser src --output-type dot | dot -T svg > dependency-graph.svg", "analyze:circular": "madge --circular --extensions ts,tsx src/", "analyze:all": "npm run analyze:unused && npm run analyze:deps && npm run analyze:circular", "clean:unused": "npm run analyze:unused:fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}