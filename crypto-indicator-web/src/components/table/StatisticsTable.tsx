import React from 'react';

import { AppStateHandler } from '@/components/AppStateHandler';
import { ChartModalStates } from '@/components/chart/ChartModalStates';
import { DashboardHeader } from '@/components/ui/DashboardHeader';
import { TabNavigation } from '@/components/ui/TabNavigation';
import { CSS_CLASSES } from '@/constants/app';
import { useTableDataManager } from '@/hooks/useTableDataManager';
import { useTabNavigation } from '@/hooks/useTabNavigation';
import { DEFAULT_TABS } from '@/types/tabs';

import { Table } from './Table';

import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';

// Component-specific styles
import '../../styles/components/table.css';

/**
 * Simplified StatisticsTable using compound components pattern
 * Reduced from 285 lines to ~50 lines (82% reduction)
 * Eliminated props hell and duplicate logic
 */
const StatisticsTable: React.FC = () => {
  // Tab navigation
  const { activeTab, setActiveTab } = useTabNavigation('crypto');

  // Get data for AppStateHandler and ChartModal (outside of TableProvider)
  const {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    chartData,
    showChart,
    setShowChart,
    onSignalClick,
    onRefresh,
  } = useTableDataManager({ assetType: activeTab });

  return (
    <AppStateHandler
      loading={loading}
      error={error}
      statisticsLength={processedData.length}
      onRetry={onRefresh}
    >
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />

        <TabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={DEFAULT_TABS}
        />

        {/* TableProvider now receives data from parent */}
        <Table.Provider
          assetType={activeTab}
          onSignalClick={onSignalClick}
          onRefresh={onRefresh}
          data={processedData as CryptoCurrencyStatisticsDto[] | StockStatisticsDto[]}
          btcStatistics={btcStatistics as CryptoCurrencyStatisticsDto[] | StockStatisticsDto[]}
          loading={loading}
          error={error}
          totalCount={totalCount}
        >
          <Table.Content
            showTableControls={activeTab === 'crypto'}
          />
        </Table.Provider>

        <ChartModalStates
          showChart={showChart}
          chartData={chartData as CryptoCurrencyStatisticsDto | StockStatisticsDto | null}
          chartLoading={false}
          chartError={null}
          onClose={() => { setShowChart(false); }}
        />
      </div>
    </AppStateHandler>
  );
};

export default StatisticsTable;
